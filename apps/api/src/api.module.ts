import { Modu<PERSON>, <PERSON>ope } from '@nestjs/common';
import { ApiV1Controller } from './v1/api.v1.controller';
import { ApiV1Service } from './v1/api.v1.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import {
  AllExceptionsFilter,
  AUTH_SERVICE,
  CacheManagerModule,
  FreezePipe,
  INTERCITY_BUS_SERVICE,
  LoggingInterceptor,
  RENTAL_SERVICE,
  WATER_TRANSPORT_SERVICE,
} from '@libs/common';
import { RepositoryModule } from '@libs/repository';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { SharedModule } from '@libs/shared';
import { TasksModule } from './tasks/tasks.module';
import { PaymentServiceModule } from './payment-service';
import {
  AUTH_SERVICE_HOST,
  AUTH_SERVICE_PORT,
  INTERCITY_BUS_HOST,
  INTERCITY_BUS_PORT,
  JWT_SECRET,
  RENTAL_HOST,
  RENTAL_PORT,
  WATER_TRANSPORT_HOST,
  WATER_TRANSPORT_PORT,
} from './common';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['./apps/api/.env', './.env'],
    }),
    RepositoryModule,
    SharedModule,
    TasksModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => {
        return {
          secret: config.get(JWT_SECRET),
        };
      },
    }),
    ClientsModule.registerAsync([
      {
        name: INTERCITY_BUS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get(INTERCITY_BUS_HOST),
            port: configService.get(INTERCITY_BUS_PORT),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get(AUTH_SERVICE_HOST),
            port: configService.get(AUTH_SERVICE_PORT),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: WATER_TRANSPORT_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get(WATER_TRANSPORT_HOST),
            port: configService.get(WATER_TRANSPORT_PORT),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: RENTAL_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get(RENTAL_HOST),
            port: configService.get(RENTAL_PORT),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    CacheManagerModule,
    PaymentServiceModule,
  ],
  controllers: [ApiV1Controller],
  providers: [
    ApiV1Service,
    {
      provide: APP_INTERCEPTOR,
      scope: Scope.REQUEST,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: FreezePipe,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class ApiModule {}
