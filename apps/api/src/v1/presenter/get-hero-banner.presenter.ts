import { HeroBannerDeviceTypeEnum } from '@libs/common';

export type ImagesType = {
  url: string;
  type: HeroBannerDeviceTypeEnum;
};

export type HeroBannerType = {
  id?: string;
  title: string;
  status: boolean;
  sequence: number;
  images: ImagesType[];
  createdAt: Date;
  updatedAt: Date;
};

type PresenterImage = {
  url: string | null;
  type: HeroBannerDeviceTypeEnum;
};

export class GetHeroBannerPresenter {
  id?: string;
  title: string;
  status: boolean;
  sequence: number;
  createdAt: Date;
  updatedAt: Date;
  images: PresenterImage[];

  constructor(type: HeroBannerType) {
    this.id = type.id;
    this.status = type.status;
    this.sequence = type.sequence;
    this.title = type.title;
    this.createdAt = type.createdAt;
    this.updatedAt = type.updatedAt;
    this.images = type.images.map((image) => ({
      url: image.url,
      type: image.type,
    }));
  }
}
