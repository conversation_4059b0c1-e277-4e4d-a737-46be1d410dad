import { IDataServices } from '@libs/repository';
import { CacheManagerService } from '@libs/common';
import { GetHeroBannerPresenter } from '../presenter/get-hero-banner.presenter';
import { HERO_BANNER } from '../../common';

export const getHeroBanner = async (
  iDataServices: IDataServices,
  cacheService: CacheManagerService,
  type: string | null = null,
) => {
  const cachedData = await cacheService.get(HERO_BANNER);

  let filteredData = [];
  let data = [];

  if (cachedData) {
    data = cachedData;
  } else {
    data = await iDataServices.heroBanner.findAll(
      {
        status: true,
      },
      {
        options: {
          sort: {
            sequence: 1,
          },
        },
      },
    );
    await cacheService.set(HERO_BANNER, data);
  }

  if (type) {
    filteredData = data
      .map((item) => {
        const filteredImages = item.images.filter(
          (image) => image.type === type,
        );
        if (filteredImages.length > 0) {
          return {
            ...item,
            images: filteredImages,
          };
        }
        return null;
      })
      .filter((item) => item !== null); // remove entries with no matching images
  } else {
    filteredData = data;
  }

  return filteredData.map((item) => new GetHeroBannerPresenter(item));
};
