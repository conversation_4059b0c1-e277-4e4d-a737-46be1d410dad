import { GetBookingsV1Dto } from '../dto';
import { UserEntity } from '@libs/repository';
import { firstValueFrom } from 'rxjs';
import { Module } from '@libs/common';
import { BookingInterface } from '@libs/common/interface/booking.interface';
import { ClientProxy } from '@nestjs/microservices';

export const getBookings = async (
  getBookingsV1Dto: GetBookingsV1Dto,
  user: UserEntity,
  intercityBusService: ClientProxy,
  waterTransportService: ClientProxy,
  rentalService: ClientProxy,
) => {
  const { module, pnrCode } = getBookingsV1Dto;
  const createBookingCall = (service) =>
    firstValueFrom(
      service.send('get-bookings', {
        ...(pnrCode && { pnrCode }),
        user,
      }),
    );

  const servicesToCall = [];
  if (!module || module === Module.BUS) {
    servicesToCall.push(createBookingCall(intercityBusService));
  }
  if (!module || module === Module.LAUNCH) {
    servicesToCall.push(createBookingCall(waterTransportService));
  }
  if (!module || module === Module.RENTAL) {
    servicesToCall.push(createBookingCall(rentalService));
  }

  const bookingsSettled = await Promise.allSettled(servicesToCall);

  const bookings: BookingInterface[] = [];
  bookingsSettled.map((booking) => {
    if (booking.status == 'fulfilled') {
      bookings.push(...booking.value.data);
    }
  });

  bookings.sort((a, b) => {
    const statusPriority = {
      RENTAL: {
        BIDDING_ONGOING: 1, // Highest priority
        CONFIRMED: 2, // Second priority
        IN_PROGRESS: 3, // Third priority
      },
    };

    const priorityA = statusPriority[a.module]?.[a.bookingStatus] ?? Infinity;
    const priorityB = statusPriority[b.module]?.[b.bookingStatus] ?? Infinity;

    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    return (
      new Date(b.bookingDateTime).getTime() -
      new Date(a.bookingDateTime).getTime()
    );
  });

  return bookings;
};
