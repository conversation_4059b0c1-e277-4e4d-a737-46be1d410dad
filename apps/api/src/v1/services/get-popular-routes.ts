import { GetPopularRoutesDto } from '../dto';
import { IDataServices } from '@libs/repository';

export const getPopularRoutes = async (
  getPopularRoutesDto: GetPopularRoutesDto,
  iDataServices: IDataServices,
) => {
  const { journeyMode } = getPopularRoutesDto;

  return await iDataServices.popularRoutes.aggregation([
    {
      $match: {
        status: true,
      },
    },
    {
      $lookup: {
        from: 'cities',
        localField: 'fromCity',
        foreignField: '_id',
        as: 'fromCity',
      },
    },
    {
      $unwind: {
        path: '$fromCity',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'cities',
        localField: 'toCity',
        foreignField: '_id',
        as: 'toCity',
      },
    },
    {
      $unwind: {
        path: '$toCity',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$journeyModes',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        ...(journeyMode && { journeyModes: journeyMode }),
      },
    },
    {
      $project: {
        _id: 0,
        id: '$_id',
        journeyMode: '$journeyModes',
        name: {
          $concat: ['$fromCity.name', ' to ', '$toCity.name'],
        },
      },
    },
  ]);
};
