import { IDataServices } from '@libs/repository';
import * as turf from '@turf/turf';
import { GetPolygonLocationDto } from '../dto';
import { CacheManagerService } from '@libs/common';
import { POLYGON_CACHE_TOKEN } from '../../common';

export const getPolygonLocation = async (
  iDataServices: IDataServices,
  cacheService: CacheManagerService,
  getPolygonLocationDto: GetPolygonLocationDto,
) => {
  const { lat, lng } = getPolygonLocationDto;
  const turfPoint = turf.point([parseFloat(lng), parseFloat(lat)]);

  let cachedCities = await cacheService.get(POLYGON_CACHE_TOKEN);

  if (!cachedCities) {
    cachedCities = await iDataServices.cities.getPolygonCities({
      status: true,
    });
    await cacheService.set(
      POLYGON_CACHE_TOKEN,
      cachedCities,
      1000 * 60 * 60 * 24 * 30,
    );
  }

  for (let city of cachedCities) {
    if (!city.polygon || city.polygon.length < 4) continue;

    const polygonCoordinates = city.polygon.map((point) => [point.y, point.x]);

    if (
      polygonCoordinates[0][0] !==
        polygonCoordinates[polygonCoordinates.length - 1][0] ||
      polygonCoordinates[0][1] !==
        polygonCoordinates[polygonCoordinates.length - 1][1]
    ) {
      polygonCoordinates.push(polygonCoordinates[0]);
    }

    const turfPolygon = turf.polygon([polygonCoordinates]);
    if (turf.booleanPointInPolygon(turfPoint, turfPolygon)) {
      return {
        id: city._id,
        name: city.name,
        lat: city.lat,
        lng: city.lng,
        division: city.division,
        isSubCity: city.isSubCity,
      };
      break;
    }
  }

  return null;
};
