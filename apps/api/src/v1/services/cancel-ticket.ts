import { IDataServices } from '@libs/repository';
import { CancelTicketV1Dto } from '../dto';
import { BadRequestException } from '@nestjs/common';
import { BookingPaymentStatus, BookingStatus, Module } from '@libs/common';
import { firstValueFrom } from 'rxjs';
import { ClientProxy } from '@nestjs/microservices';

export const cancelTicket = async (
  cancelTicketV1Dto: CancelTicketV1Dto,
  iDataServices: IDataServices,
  intercityBusService: ClientProxy,
  waterTransportService: ClientProxy,
) => {
  try {
    const {
      reference,
      module,
      remark,
      cancelledByName,
      cancelledByPhone,
      forcedCancel,
    } = cancelTicketV1Dto;

    const bookingHistory = await iDataServices.bookingHistories.findOne({
      filter: {
        module,
        referenceId: reference,
      },
    });

    if (!bookingHistory)
      throw new BadRequestException('Booking History not found!');

    const cancelPayload = {
      module,
      subModule: bookingHistory.subModule,
      referenceId: reference,
      cancelledByName,
      cancelledByPhone,
      remark: remark ? remark : '',
      forcedCancel,
    };
    let cancelResponse;

    try {
      switch (bookingHistory.module) {
        case Module.BUS:
          cancelResponse = await firstValueFrom(
            intercityBusService.send('cancel-ticket-confirm', cancelPayload),
          );
          break;
        case Module.LAUNCH:
          cancelResponse = await firstValueFrom(
            waterTransportService.send('cancel-ticket-confirm', cancelPayload),
          );
          break;
      }
    } catch (e) {
      throw new BadRequestException(e.message);
    }

    if (!cancelResponse.status) throw new Error(cancelResponse.message);

    return await iDataServices.bookingHistories.update(
      { id: bookingHistory.id },
      {
        bookingStatus: BookingStatus.CANCELLED,
        paymentStatus: BookingPaymentStatus.REFUND_PENDING,
      },
    );
  } catch (err) {
    throw err;
  }
};
