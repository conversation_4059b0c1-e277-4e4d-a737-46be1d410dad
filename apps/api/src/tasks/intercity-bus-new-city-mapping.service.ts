import { Inject, Injectable } from '@nestjs/common';
import {
  AbstractSendNotification,
  areObjectsEqual<PERSON><PERSON><PERSON>,
  CronCheck,
  IBaseTask,
  INTERCITY_BUS_SERVICE,
  Module,
  SubModule,
} from '@libs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { IDataServices, TpcityEntity } from '@libs/repository';
import { firstValueFrom } from 'rxjs';
import { ClientProxy } from '@nestjs/microservices';

@Injectable()
export class IntercityBusNewCityMappingService extends IBaseTask {
  constructor(
    @Inject(INTERCITY_BUS_SERVICE)
    private readonly intercityBusService: ClientProxy,
    protected readonly configService: ConfigService,
    private readonly iDataServices: IDataServices,
    private readonly abstractNotification: AbstractSendNotification,
  ) {
    super();
  }

  @Cron(CronExpression.EVERY_DAY_AT_7PM)
  @CronCheck('CITY_MAP_CRON_ENABLED')
  async handler() {
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: 'Cron starting...',
    });

    await this.hanifCities();
    await this.jatriIntercityCities();
    await this.jatriV4IntercityCities();
    await this.shohaghCities();
    await this.paribahanCities();
    await this.londonExpressCities();

    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `Cron ending...`,
    });
  }

  private async jatriIntercityCities() {
    const newCities = [];
    const jatriNewPanelCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.JATRI_INTERCITY,
      }),
    );
    for (const city of jatriNewPanelCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city, subModule: SubModule.JATRI_INTERCITY },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city,
          module: Module.BUS,
          subModule: SubModule.JATRI_INTERCITY,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.JATRI_INTERCITY}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async jatriV4IntercityCities() {
    const newCities = [];
    const jatriV4NewPanelCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.JATRI_INTERCITY_V4,
      }),
    );
    for (const city of jatriV4NewPanelCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city.name, subModule: SubModule.JATRI_INTERCITY_V4 },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city.name,
          module: Module.BUS,
          subModule: SubModule.JATRI_INTERCITY_V4,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city.name,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.JATRI_INTERCITY_V4}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async paribahanCities() {
    const newCities = [];
    const paribahanCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.PARIBAHAN,
      }),
    );
    for (const city of paribahanCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city.name, subModule: SubModule.PARIBAHAN },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city.name,
          module: Module.BUS,
          subModule: SubModule.PARIBAHAN,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city.name,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.PARIBAHAN}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async hanifCities() {
    const newCities = [];
    const hanifCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.HANIF,
      }),
    );
    for (const city of hanifCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city.name, subModule: SubModule.HANIF },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city.name,
          module: Module.BUS,
          subModule: SubModule.HANIF,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city.name,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.HANIF}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async shohaghCities() {
    const newCities = [];
    const shohaghCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.SHOHAGH,
      }),
    );
    for (const city of shohaghCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city.name, subModule: SubModule.SHOHAGH },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city.name,
          module: Module.BUS,
          subModule: SubModule.SHOHAGH,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city.code,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.SHOHAGH}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async londonExpressCities() {
    const newCities = [];
    const londonExpressCities = await firstValueFrom(
      this.intercityBusService.send('get-tp-cities', {
        subModuleType: SubModule.LONDON_EXPRESS,
      }),
    );
    for (const city of londonExpressCities) {
      const exist = await this.iDataServices.tpCities.update(
        { name: city.name, subModule: SubModule.LONDON_EXPRESS },
        { syncDatetime: new Date(), details: city },
      );
      if (!exist) {
        // new one
        const newCity = await this.iDataServices.tpCities.insert({
          name: city.name,
          module: Module.BUS,
          subModule: SubModule.LONDON_EXPRESS,
          syncDatetime: new Date(),
          isPending: true,
          details: city,
          search: city.distID,
        });
        newCities.push(newCity.name);
      } else {
        this.checkChangesAndNotify(exist, city);
      }
    }
    await this.abstractNotification.sendNotification({
      origin: this.configService.get('URL'),
      taskType: 'New intercity cities sync from 3rd party clients.',
      message: `New cities found in ${SubModule.LONDON_EXPRESS}: ${newCities.length}\n${JSON.stringify(newCities)}`,
    });
  }

  private async checkChangesAndNotify(
    existTpCity: TpcityEntity,
    cityFromTP: any,
  ) {
    if (!areObjectsEqualHelper(existTpCity.details, cityFromTP)) {
      await this.abstractNotification.sendNotification({
        origin: this.configService.get('URL'),
        taskType: 'New intercity cities sync from 3rd party clients.',
        message: `Found changes in city!!!\nExist: ${existTpCity.details}\nFound: ${cityFromTP}`,
      });
    }
  }
}
