import { RentalPartnerEntity } from '@libs/repository';
import { ConfigService } from '@nestjs/config';

export const getRemainingRecoveryTime = (
  partner: RentalPartnerEntity,
  configService: ConfigService,
) => {
  const recoveryDays = configService.get('RECOVERY_IN_DAYS') || 30;
  const deletedTimestamp = partner?.deletedAt.getTime();
  const recoveryDeadline =
    deletedTimestamp + recoveryDays * 24 * 60 * 60 * 1000;

  const now = Date.now();

  return recoveryDeadline - now;
};
