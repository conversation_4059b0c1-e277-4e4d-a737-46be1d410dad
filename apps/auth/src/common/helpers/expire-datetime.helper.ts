// export function expireDateTime(hour: number) {
//   const curDate = new Date();
//   const nextDate = new Date();
//   nextDate.setHours(hour, 0, 0);
//   if (curDate.getHours() >= nextDate.getHours()) {
//     nextDate.setDate(nextDate.getDate() + 1);
//   }
//   return nextDate;
// }

export function expireDateTime(durationStr: string): Date {
  const curDate = new Date();
  const resultDate = new Date(curDate);

  const regex = /^(\d+)([smhdMy])$/;
  const match = regex.exec(durationStr);

  if (!match) {
    throw new Error(
      `Invalid duration format: '${durationStr}'. Use formats like '1d', '30m', '2y', '1M'.`,
    );
  }

  const value = parseInt(match[1], 10);
  const unit = match[2];

  switch (unit) {
    case 's':
      resultDate.setSeconds(resultDate.getSeconds() + value);
      break;
    case 'm':
      resultDate.setMinutes(resultDate.getMinutes() + value);
      break;
    case 'h':
      resultDate.setHours(resultDate.getHours() + value);
      break;
    case 'd':
      resultDate.setDate(resultDate.getDate() + value);
      break;
    case 'M':
      resultDate.setMonth(resultDate.getMonth() + value);
      break;
    case 'y':
      resultDate.setFullYear(resultDate.getFullYear() + value);
      break;
    default:
      throw new Error(`Invalid duration unit: ${unit}`);
  }

  return resultDate;
}
