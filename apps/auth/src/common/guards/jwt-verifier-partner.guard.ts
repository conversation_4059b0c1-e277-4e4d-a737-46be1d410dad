import {
  CanActivate,
  ConflictException,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { RpcException } from '@nestjs/microservices';
import { CacheManagerService } from '@libs/common';
import { AuthCacheKeyEnum } from '../../rental-partner/enum';
import { IDataServices } from '@libs/repository';

@Injectable()
export class JwtVerifierPartnerGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private iDataService: IDataServices,
    private readonly cacheManagerService: CacheManagerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const contextType = context.getType<'http' | 'rpc'>();
    const token = this.extractToken(context, contextType);
    if (!token) {
      this.throwException(
        contextType,
        new ForbiddenException('You are not logged in!'),
      );
    }

    let payload;
    try {
      payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });
    } catch (e) {
      return this.throwException(
        contextType,
        new UnauthorizedException('Your token is expired'),
      );
    }
    const cacheKey = `${AuthCacheKeyEnum.RENTAL_PARTNER_AUTH_TOKEN}_${payload.id}`;
    const cachedToken = await this.cacheManagerService.get(cacheKey);

    if (cachedToken?.accessToken !== token) {
      this.throwException(
        contextType,
        new ConflictException(
          'Your token is invalid or you may be logged in from another device',
        ),
      );
    }

    const currentTime = Math.floor(Date.now() / 1000);
    if (payload.exp < currentTime) {
      this.throwException(
        contextType,
        new UnauthorizedException('Your token is expired'),
      );
    }

    this.attachUserToContext(context, contextType, payload);
    return true;
  }

  private extractToken(
    context: ExecutionContext,
    contextType: 'http' | 'rpc',
  ): string | undefined {
    if (contextType === 'http') {
      const request = context.switchToHttp().getRequest();
      const authHeader = request.headers?.authorization;
      return authHeader.toString().split(' ')[1];
    } else if (contextType === 'rpc') {
      const data = context.switchToRpc().getData();
      const authHeader = data?.Authentication;
      return authHeader?.split(' ')[1];
    }
    return undefined;
  }

  private attachUserToContext(
    context: ExecutionContext,
    contextType: 'http' | 'rpc',
    payload: any,
  ): void {
    if (contextType === 'http') {
      const request = context.switchToHttp().getRequest();
      request.user = payload;
    } else if (contextType === 'rpc') {
      const data = context.switchToRpc().getData();
      data.user = payload;
    }
  }

  private throwException(
    contextType: 'http' | 'rpc',
    exception: UnauthorizedException | ForbiddenException | ConflictException,
  ): never {
    if (contextType === 'rpc') {
      throw new RpcException(exception);
    } else {
      throw exception;
    }
  }
}
