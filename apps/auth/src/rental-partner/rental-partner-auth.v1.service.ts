import { IDataServices, ISession } from '@libs/repository';
import {
  BadGatewayException,
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable,
} from '@nestjs/common';
import {
  LoginDto,
  RefreshTokenDto,
  ResetPasswordV1Dto,
  VerifyDto,
} from './dto';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  CacheManagerService,
  generateRandomString,
  OtpPurpose,
  PartnerStatus,
  S3Service,
} from '@libs/common';
import { getRemainingRecoveryTime, OTPService } from '../common';
import {
  RegistrationDto,
  SendOtpDto,
} from '../../../rental-partner/src/v1/profile/dto';
import {
  CreditTransactionCategoryType,
  CreditTransactionType,
  ForgetPasswordType,
} from '../../../rental-partner/src/common';
import mongoose, { ClientSession } from 'mongoose';
import * as argon2 from 'argon2';
import { AuthCacheKeyEnum } from './enum';

@Injectable()
export class RentalPartnerAuthV1Service {
  constructor(
    private readonly coreDataServices: IDataServices,
    private readonly otpService: OTPService,
    private readonly configService: ConfigService,
    private jwtService: JwtService,
    private readonly s3Service: S3Service,
    private readonly dbSession: ISession<mongoose.ClientSession>,
    private readonly cacheManagerService: CacheManagerService,
  ) {}

  async sendOtp(sendOtpDto: SendOtpDto) {
    try {
      const { phone, forgetPassword, purpose } = sendOtpDto;

      if (forgetPassword) {
        const user =
          await this.coreDataServices.rentalPartners.getPartnerByPhone(phone);
        if (forgetPassword == ForgetPasswordType.YES && user == null) {
          throw new BadRequestException('আপনি নিবন্ধিত নন');
        } else if (
          forgetPassword == ForgetPasswordType.NO &&
          user &&
          (!user.deletedAt || (await this.remainingRecoveryDays(user.id)) > 0)
        ) {
          throw new BadRequestException('You are already registered!');
        }
      }
      const msg = '[Jatri.co] Your verification code is: __otpCode__';
      return await this.otpService.sendOtp(phone, msg, purpose);
    } catch (e) {
      console.log('===', e);
      throw new BadRequestException(
        e?.response?.message ?? e?._message ?? 'Something Went Wrong',
      );
    }
  }

  async login(loginDto: LoginDto) {
    try {
      await this.dbSession.start();
      const session: ClientSession = this.dbSession.get();
      const { phone, password } = loginDto;
      const partner = await this.coreDataServices.rentalPartners.findOne({
        filter: { phone },
        projection: '+password',
      });
      if (!partner) {
        throw new BadRequestException(
          'You have to complete registration first',
        );
      }
      const remainingRecoveryDays = await this.remainingRecoveryDays(
        partner.id,
      );

      if (partner && partner.deletedAt && remainingRecoveryDays < 0) {
        throw new BadRequestException(
          'You have to complete registration first',
        );
      }

      if (!(await argon2.verify(partner.password, password))) {
        throw new BadRequestException('ফোন নাম্বার / পাসওয়ার্ড টি ভুল হয়েছে!');
      }

      const [accessToken, refreshToken] = await Promise.all([
        this.generateAccessToken(partner),
        this.generateRefreshToken(partner),
      ]);

      const decodeRefToken = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_RESET_SECRET'),
      });

      const cacheKey = `${AuthCacheKeyEnum.RENTAL_PARTNER_AUTH_TOKEN}_${partner.id}`;

      // recover account
      await this.coreDataServices.rentalPartners.update(
        {
          id: partner.id,
        },
        {
          deletedAt: null,
          recoveryExpireAt: null,
        },
        {
          options: {
            session,
          },
        },
      );
      await this.coreDataServices.rentalDrivers.update(
        {
          partner,
        },
        {
          isDeleted: false,
        },
        {
          options: {
            session,
          },
        },
      );
      await this.coreDataServices.rentalVehicles.update(
        {
          partner,
        },
        {
          deletedAt: null,
        },
        {
          options: {
            session,
          },
        },
      );

      await this.coreDataServices.rentalPartnerAuth.upsert(
        {
          partner,
        },
        {
          accessToken,
          refreshToken,
        },
        {
          saveOptions: { session },
        },
      );
      await this.dbSession.commit();

      await this.cacheManagerService.set(
        cacheKey,
        { accessToken, refreshToken },
        decodeRefToken.exp * 1000 - Date.now(),
      );
      return { accessToken, refreshToken };
    } catch (e) {
      console.log('===', e);
      await this.dbSession.abort();
      throw new BadRequestException(
        e?.response?.message ?? e?._message ?? 'Something Went Wrong',
      );
    } finally {
      await this.dbSession.end();
    }
  }

  async refreshToken(refreshDto: RefreshTokenDto) {
    try {
      const { refreshToken: token } = refreshDto;
      let decodeToken;
      try {
        decodeToken = this.jwtService.verify(token, {
          secret: this.configService.get('JWT_RESET_SECRET'),
        });
      } catch (e) {
        throw new ForbiddenException('Token expired!');
      }
      if (!decodeToken?.id) {
        throw new ForbiddenException('Invalid Token!');
      }

      const partner = await this.coreDataServices.rentalPartners.findOne({
        filter: { id: decodeToken.id },
      });

      if (!partner || !partner.status) {
        new ForbiddenException('Access Denied');
      }

      const cacheKey = `${AuthCacheKeyEnum.RENTAL_PARTNER_AUTH_TOKEN}_${partner.id}`;
      const cachedToken = await this.cacheManagerService.get(cacheKey);
      if (cachedToken?.refreshToken) {
        if (cachedToken.refreshToken !== token) {
          throw new BadRequestException(
            'Already logged in with another device!',
          );
        }
      } else {
        throw new ForbiddenException('Invalid Token!');
      }

      const [accessToken, refreshToken] = await Promise.all([
        this.generateAccessToken(partner),
        this.generateRefreshToken(partner),
      ]);

      // update new token is in db
      await this.coreDataServices.rentalPartnerAuth.upsert(
        {
          partner,
        },
        {
          accessToken,
          refreshToken,
        },
      );

      // update new token in cache
      const data = {
        accessToken,
        refreshToken,
      };

      const decodedRefToken = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_RESET_SECRET'),
      });
      await this.cacheManagerService.delete(cacheKey);
      await this.cacheManagerService.set(
        cacheKey,
        data,
        decodedRefToken.exp * 1000 - Date.now(),
      );

      return { accessToken, refreshToken };
    } catch (e) {
      if (e instanceof HttpException) {
        throw e;
      }
      throw new BadRequestException(
        e?.response?.message ?? e?._message ?? e ?? 'Something Went Wrong',
      );
    }
  }

  async verifyOtp(verifyDto: VerifyDto) {
    const { code, phone } = verifyDto;
    try {
      await this.otpService.verifyOtp(phone, code);
    } catch (e) {
      throw new BadRequestException(
        e?.response?.message ?? 'Something Went Wrong',
      );
    }
    return 'OTP verified successfully';
  }

  async registration(input: RegistrationDto) {
    let accessToken;
    let refreshToken;
    const {
      gender,
      secondaryPhone,
      phone,
      email,
      referralCode,
      address,
      deviceToken,
      division,
      code,
      name,
      password,
      avatar,
      dateOfBirth,
      nid,
      passport,
    } = input;
    let { city } = input;

    try {
      await this.dbSession.start();
      const session: ClientSession = this.dbSession.get();
      const user =
        await this.coreDataServices.rentalPartners.getPartnerByPhone(phone);

      if (user && !user.deletedAt) {
        const remainingRecoveryDays = await this.remainingRecoveryDays(user.id);
        if (remainingRecoveryDays < 0) {
          throw new BadRequestException('Phone number already exists');
        }
      }

      if (email) {
        const checkEmail =
          await this.coreDataServices.rentalPartners.getPartnerByEmail(email);
        if (checkEmail) throw new BadRequestException('Email already exists');
      }

      const otpInfo = await this.coreDataServices.otps.findOne({
        filter: {
          expireAt: {
            $gte: new Date(),
          },
          phone,
          code,
          isVerified: true,
        },
      });

      if (!otpInfo) {
        throw new BadRequestException('OTP verification error');
      }

      let refBy = '';
      if (referralCode) {
        const referenceByPartner =
          await this.coreDataServices.rentalPartners.findOne({
            filter: { refCode: referralCode },
          });
        if (referenceByPartner) {
          refBy = referenceByPartner.id;
        }
      }

      let image = '';
      if (avatar) {
        image = await this.s3Service.storeImage(
          avatar,
          generateRandomString(15),
          'dt-mm/rental/partner',
        );
      }

      const regBonus =
        await this.coreDataServices.rentalRegistrationBonuses.findAll({});
      const refCode = Date.now().toString(36).toUpperCase();

      // TODO:: rework on cache
      // const bonusSet = await this.cacheManagerService.get(
      //   CacheKeyEnum.RENTAL_PATNER_REGISTRATION_BONUS_CREDIT_CACHE_KEY,
      // );

      const partner = await this.coreDataServices.rentalPartners.insert(
        {
          phone,
          password: await argon2.hash(password),
          name,
          secondaryPhone,
          gender,
          dateOfBirth: new Date(dateOfBirth),
          nid,
          passport,
          address: {
            address,
            division,
            city: city || null,
          },
          email: email != '' ? email : null,
          refBy,
          refCode,
          avatar: image,
          deviceToken: deviceToken ?? '',
          remarks: '',
          status: PartnerStatus.ONBOARD,
          tag: '',
          vehicleStatus: false,
          bookingCount: 0,
          rating: '5',
        },
        {
          saveOptions: { session },
        },
      );

      const formattedData = regBonus.reduce(
        (acc, item) => {
          acc[item.city.toString()] = item;
          return acc;
        },
        {} as Record<string, any>,
      );

      if (!city || city === undefined) {
        const findCityByDivision = await this.coreDataServices.cities.findOne({
          filter: { division: division },
        });

        city = findCityByDivision.id;
      }

      if (formattedData[city]) {
        const bonus = formattedData[city]['bonusCredit'];

        if (bonus > 0) {
          const expirationDate = new Date();
          expirationDate.setDate(
            expirationDate.getDate() + formattedData[city]['expireInDay'],
          );
          expirationDate.setHours(17, 59, 59, 999);

          await this.coreDataServices.rentalPartners.update(
            {
              id: partner.id,
            },
            {
              bonusCredit: bonus,
              bonusExpiration: expirationDate,
            },
            {
              options: {
                session,
              },
            },
          );

          await this.coreDataServices.rentalCreditTransactions.insert(
            {
              partner,
              previous: 0,
              processed: bonus,
              current: bonus,
              type: CreditTransactionType.CREDIT,
              category: CreditTransactionCategoryType.REG_BONUS,
              commission: 0,
              fromBonusCredit: 0,
              fromCredit: 0,
              remark: 'Registration bonus credit',
              serviceCharge: 0,
            },
            {
              saveOptions: { session },
            },
          );
        }
      }

      [accessToken, refreshToken] = await Promise.all([
        this.generateAccessToken(partner),
        this.generateRefreshToken(partner),
      ]);
      const decodeRefToken = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_RESET_SECRET'),
      });

      const cacheKey = `${AuthCacheKeyEnum.RENTAL_PARTNER_AUTH_TOKEN}_${partner.id}`;
      await this.cacheManagerService.set(
        cacheKey,
        { accessToken, refreshToken },
        decodeRefToken.exp * 1000 - Date.now(),
      );

      await this.dbSession.commit();
    } catch (e) {
      await this.dbSession.abort();
      console.log(e);
      throw new BadRequestException(
        e?.response?.message ?? e?._message ?? 'Something Went Wrong',
      );
    } finally {
      await this.dbSession.end();
    }

    return { accessToken, refreshToken };
  }

  async profile(id: string) {
    const user = await this.coreDataServices.users.findById(id);

    if (!user) {
      throw new BadGatewayException('User not found!');
    }

    return user;
  }

  async resetPassword(resetPasswordDto: ResetPasswordV1Dto) {
    const { phone, password, code } = resetPasswordDto;

    const partner = await this.coreDataServices.rentalPartners.findOne({
      filter: { phone },
    });
    if (!partner) {
      throw new BadRequestException('Phone number not exists');
    }

    if (
      partner.deletedAt != null &&
      getRemainingRecoveryTime(partner, this.configService) < 0
    ) {
      throw new BadRequestException('You have to complete registration first');
    }

    try {
      // await this.otpService.verifyOtp(phone, code);

      const cachedOtp = await this.coreDataServices.otps.findOne({
        filter: {
          expireAt: {
            $gte: new Date(),
          },
          phone,
          code,
          purpose: OtpPurpose.RESET_PASSWORD,
        },
      });
      if (!cachedOtp) {
        throw new BadRequestException('Invalid OTP , Please try again !');
      }
    } catch (e) {
      throw new BadRequestException(
        e?.response?.message ?? 'Something Went Wrong',
      );
    }

    return await this.coreDataServices.rentalPartners.update(
      { phone },
      {
        password: await argon2.hash(password),
      },
    );
  }

  async remainingRecoveryDays(partnerId: string): Promise<number> {
    const partner = await this.coreDataServices.rentalPartners.findOne({
      filter: { id: partnerId },
    });
    const today = new Date();

    if (!partner || today > new Date(partner.recoveryExpireAt)) {
      return -1;
    }

    const recoveryExpireDate = new Date(partner.recoveryExpireAt);

    // Calculate difference in days
    const diffInTime = recoveryExpireDate.getTime() - today.getTime();
    const diffInDays = Math.ceil(diffInTime / (1000 * 60 * 60 * 24));

    return 1 + diffInDays;
  }

  private generateAccessToken(partner) {
    const jwtExpiresIn = this.configService.get('JWT_EXPIRES_IN');

    return this.jwtService.sign(
      {
        id: partner.id,
        name: partner.name,
        phone: partner.phone,
        email: partner.email,
      },
      {
        secret: this.configService.get('JWT_SECRET'),
        expiresIn: `${jwtExpiresIn}`,
      },
    );
  }

  private generateRefreshToken(partner) {
    const jwtRefreshExpiresIn = this.configService.get(
      'JWT_REFRESH_EXPIRES_IN',
    );

    return this.jwtService.sign(
      {
        id: partner.id,
        name: partner.name,
        phone: partner.phone,
        email: partner.email,
      },
      {
        secret: this.configService.get('JWT_RESET_SECRET'),
        expiresIn: `${jwtRefreshExpiresIn}`,
      },
    );
  }
}
