import { DeviceType, LoginType } from '@libs/common';
import { IsEnum, IsNotEmpty, IsString, ValidateIf } from 'class-validator';

export class LoginDto {
  @IsEnum(LoginType)
  loginType: LoginType;

  @ValidateIf((obj) => obj.loginType != LoginType.PHONE)
  @IsEnum(DeviceType)
  deviceType: DeviceType;

  @ValidateIf((obj) => obj.loginType === LoginType.PHONE)
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ValidateIf((obj) => obj.loginType === LoginType.GOOGLE)
  @IsString()
  @IsNotEmpty()
  token: string;
}
