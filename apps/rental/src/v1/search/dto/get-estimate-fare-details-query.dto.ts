import {
  IsDateString,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Optional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';

export class GetEstimateFareDetailsQueryDto {
  @IsString()
  @IsNotEmpty()
  pickAddress: string;

  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  pickLongitude: number;

  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  pickLatitude: number;

  @IsString()
  @IsNotEmpty()
  dropAddress: string;

  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  dropLongitude: number;

  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  dropLatitude: number;

  @IsOptional()
  @IsString()
  viaAddress: string;

  @IsOptional()
  @Type(() => Number)
  viaLongitude: number[] | number;

  @IsOptional()
  @Type(() => Number)
  viaLatitude: number[] | number;

  @IsOptional()
  @IsDateString()
  returnDateTime: string;
}
