import { Module } from '@nestjs/common';
import { SearchV1Controller } from './search.v1.controller';
import { SearchV1Service } from './search.v1.service';
import { JatriRentalV1Module } from '../../api-providers/jatri-rental/v1';
import { GoogleMapService } from '@libs/shared/google-map/google-map.service';
import { RepositoryModule } from '@libs/repository';
import { API_SERVICE, AUTH_SERVICE, CacheManagerModule } from '@libs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { SearchMessageController } from './search-message.controller';
import { CacheServices, SharedModule } from '@libs/shared';

@Module({
  imports: [
    JatriRentalV1Module,
    RepositoryModule,
    SharedModule,
    CacheManagerModule,
    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_SERVICE_HOST'),
            port: configService.get('AUTH_SERVICE_PORT'),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: API_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('API_SERVICE_HOST'),
            port: configService.get('API_SERVICE_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [SearchV1Controller, SearchMessageController],
  providers: [SearchV1Service, GoogleMapService, CacheServices],
})
export class SearchV1Module {}
