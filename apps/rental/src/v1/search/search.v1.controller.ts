import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { SearchV1Service } from './search.v1.service';
import {
  GetEstimateDistanceDto,
  GetEstimateFareDetailsQueryDto,
  GetEstimateFareQueryDto,
} from './dto';
import {
  CurrentUser,
  JwtGuestAuthGuard,
  ResponseInterceptor,
} from '@libs/common';
import { DtGuard } from '../../../../api/src/common';
import { UserEntity } from '@libs/repository';

@Controller({
  version: '1',
  path: 'search',
})
@UseGuards(DtGuard)
export class SearchV1Controller {
  constructor(private readonly rentalV1Service: SearchV1Service) {}

  @Post('get-estimate-fare')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtGuestAuthGuard)
  async getEstimateFare(
    @Body() data: GetEstimateFareQueryDto,
    @CurrentUser() user: UserEntity,
  ) {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getEstimateFare(data, user),
    };
  }

  @Get('get-estimate-fare/:id')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getEstimateFareDetails(
    @Param('id') id: string,
    @Query() query: GetEstimateFareDetailsQueryDto,
  ) {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getEstimateFareDetails(id, query),
    };
  }

  @Get('get-available-services')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getAvailableServices() {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getAvailableServices(),
    };
  }

  @Get('get-auto-complete-places')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getAutoCompletePlaces(@Query('place') place: string) {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getAutoCompletePlaces(place),
    };
  }

  @Get('get-place-details/:placeId')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getPlaceDetails(@Param('placeId') placeId: string) {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getPlaceDetails(placeId),
    };
  }

  @Get('get-insurance-details')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getInsuranceDetails() {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getInsuranceDetails(),
    };
  }

  @Get('get-payment-method-offers')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getPaymentMethodOffers() {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getPaymentMethodOffers(),
    };
  }

  @Get('get-shutdown-dataset')
  @HttpCode(HttpStatus.OK)
  async getShutdownDataset() {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getShutdownDataset(),
    };
  }

  @Post('get-estimate-distance')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  async getEstimateDistance(@Body() data: GetEstimateDistanceDto) {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getEstimateDistance(data),
    };
  }

  @Get('get-payment-details')
  @UseInterceptors(ResponseInterceptor)
  @HttpCode(HttpStatus.OK)
  @UseGuards(DtGuard)
  async getPaymentDetails() {
    return {
      message: 'Success',
      result: await this.rentalV1Service.getPaymentDetails(),
    };
  }
}
